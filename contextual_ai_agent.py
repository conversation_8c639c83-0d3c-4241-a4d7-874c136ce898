"""
Contextual AI Agent - 集成所有上下文工程技术的综合AI助手

这个Agent集成了以下技术：
1. Write: StateGraph状态管理 + InMemoryStore长期记忆
2. Select: 状态选择 + 记忆选择 + RAG检索 + BigTool工具选择  
3. Compress: 对话总结 + RAG压缩
4. Isolate: 子代理架构 + 沙盒环境 + 状态隔离
"""

import os
import getpass
import uuid
import math
import types
from typing import TypedDict, List, Dict, Any, Optional
from typing_extensions import Literal

# Core LangChain and LangGraph imports
from langchain.chat_models import init_chat_model
from langchain.embeddings import init_embeddings
from langgraph.graph import StateGraph, MessagesState, START, END
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.store.memory import InMemoryStore
from langgraph.store.base import BaseStore

# RAG and document processing
from langchain_community.document_loaders import WebBaseLoader
from langchain_core.vectorstores import InMemoryVectorStore
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain.tools.retriever import create_retriever_tool

# Tool selection and agent creation
from langgraph_bigtool import create_agent
from langgraph_bigtool.utils import convert_positional_only_function_to_tool
from langgraph.prebuilt import create_react_agent
from langgraph_supervisor import create_supervisor

# Sandbox execution
from langchain_sandbox import PyodideSandboxTool

# Message handling
from langchain_core.messages import SystemMessage, ToolMessage, HumanMessage, AIMessage

# Utilities
from rich.console import Console
from rich.pretty import pprint
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class ContextualAgentState(TypedDict):
    """综合状态管理 - 支持多种上下文类型"""
    # 基础对话状态
    messages: List[Any]
    
    # 当前任务信息
    current_task: str
    task_type: str  # "research", "math", "code", "general"
    
    # 工作记忆 (Scratchpad)
    working_memory: Dict[str, Any]
    
    # 压缩后的历史
    compressed_history: str
    
    # RAG检索结果
    retrieved_knowledge: List[str]
    
    # 工具使用历史
    tool_usage_history: List[Dict[str, Any]]
    
    # 子代理分配
    assigned_agent: Optional[str]


class ContextualAIAgent:
    """集成所有上下文工程技术的AI Agent"""
    
    def __init__(self):
        self.console = Console()
        self._setup_models()
        self._setup_storage()
        self._setup_tools()
        self._setup_sub_agents()
        self._build_main_workflow()
        
    def _setup_models(self):
        """设置模型"""
        # 确保API密钥存在
        if not os.getenv("ANTHROPIC_API_KEY"):
            os.environ["ANTHROPIC_API_KEY"] = getpass.getpass("请输入Anthropic API密钥: ")
        if not os.getenv("OPENAI_API_KEY"):
            os.environ["OPENAI_API_KEY"] = getpass.getpass("请输入OpenAI API密钥: ")
            
        self.llm = init_chat_model("anthropic:claude-3-sonnet-20240229", temperature=0)
        self.embeddings = init_embeddings("openai:text-embedding-3-small")
        
    def _setup_storage(self):
        """设置存储系统 - Write技术"""
        # 短期记忆 (Checkpointing)
        self.checkpointer = InMemorySaver()
        
        # 长期记忆 (InMemoryStore)
        self.memory_store = InMemoryStore()
        
        # 工具存储 (BigTool)
        self.tool_store = InMemoryStore(
            index={
                "embed": self.embeddings,
                "dims": 1536,
                "fields": ["description"],
            }
        )
        
        # RAG知识库
        self.knowledge_store = None
        self._setup_knowledge_base()
        
    def _setup_knowledge_base(self):
        """设置RAG知识库"""
        try:
            # 示例文档 - 可以替换为您的文档
            sample_docs = [
                "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
                "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。",
                "深度学习是机器学习的一个分支，使用神经网络来模拟人脑的工作方式。",
                "自然语言处理(NLP)是人工智能的一个领域，专注于计算机与人类语言之间的交互。"
            ]
            
            # 创建文档对象
            from langchain_core.documents import Document
            docs = [Document(page_content=content) for content in sample_docs]
            
            # 创建向量存储
            self.knowledge_store = InMemoryVectorStore.from_documents(
                documents=docs, 
                embedding=self.embeddings
            )
            
            # 创建检索工具
            self.retriever_tool = create_retriever_tool(
                self.knowledge_store.as_retriever(),
                "knowledge_search",
                "搜索相关知识和信息"
            )
            
        except Exception as e:
            self.console.print(f"[red]知识库设置失败: {e}[/red]")
            self.retriever_tool = None

    def _setup_tools(self):
        """设置工具系统 - BigTool技术"""
        # 数学工具集合
        self.math_tools = []
        self.tool_registry = {}

        # 收集math模块的所有函数
        for function_name in dir(math):
            function = getattr(math, function_name)
            if isinstance(function, types.BuiltinFunctionType):
                try:
                    if tool := convert_positional_only_function_to_tool(function):
                        self.math_tools.append(tool)
                        tool_id = str(uuid.uuid4())
                        self.tool_registry[tool_id] = tool

                        # 将工具索引到存储中
                        self.tool_store.put(
                            ("tools",),
                            tool_id,
                            {"description": f"{tool.name}: {tool.description}"}
                        )
                except Exception:
                    continue

        # 沙盒工具
        try:
            self.sandbox_tool = PyodideSandboxTool(allow_net=True)
        except Exception:
            self.sandbox_tool = None

        # 基础工具列表
        self.basic_tools = []
        if self.retriever_tool:
            self.basic_tools.append(self.retriever_tool)
        if self.sandbox_tool:
            self.basic_tools.append(self.sandbox_tool)

    def _setup_sub_agents(self):
        """设置子代理架构 - Isolate技术"""
        # 数学专家代理
        self.math_agent = create_react_agent(
            model=self.llm,
            tools=self.math_tools[:5],  # 限制工具数量避免混乱
            name="math_expert",
            prompt="你是数学专家。专门处理数学计算和分析任务。一次只使用一个工具。"
        )

        # 研究专家代理
        research_tools = [self.retriever_tool] if self.retriever_tool else []
        self.research_agent = create_react_agent(
            model=self.llm,
            tools=research_tools,
            name="research_expert",
            prompt="你是研究专家。专门处理信息检索和知识查询任务。不进行数学计算。"
        )

        # 代码专家代理
        code_tools = [self.sandbox_tool] if self.sandbox_tool else []
        self.code_agent = create_react_agent(
            model=self.llm,
            tools=code_tools,
            name="code_expert",
            prompt="你是代码专家。专门处理编程和代码执行任务。在安全沙盒中运行代码。"
        )

        # 创建监督者工作流
        try:
            self.supervisor_workflow = create_supervisor(
                [self.research_agent, self.math_agent, self.code_agent],
                model=self.llm,
                prompt=(
                    "你是团队监督者，管理研究专家、数学专家和代码专家。"
                    "根据用户查询将任务分配给合适的专家："
                    "- 信息查询和研究 → research_expert"
                    "- 数学计算和分析 → math_expert"
                    "- 编程和代码执行 → code_expert"
                )
            )
        except Exception as e:
            self.console.print(f"[yellow]监督者设置失败，将使用基础模式: {e}[/yellow]")
            self.supervisor_workflow = None

    def _build_main_workflow(self):
        """构建主工作流 - 集成所有技术"""
        # 创建主状态图
        self.workflow = StateGraph(ContextualAgentState)

        # 添加节点
        self.workflow.add_node("analyze_task", self._analyze_task)
        self.workflow.add_node("select_context", self._select_context)
        self.workflow.add_node("compress_history", self._compress_history)
        self.workflow.add_node("route_to_agent", self._route_to_agent)
        self.workflow.add_node("execute_task", self._execute_task)
        self.workflow.add_node("update_memory", self._update_memory)

        # 定义工作流
        self.workflow.add_edge(START, "analyze_task")
        self.workflow.add_edge("analyze_task", "select_context")
        self.workflow.add_edge("select_context", "compress_history")
        self.workflow.add_edge("compress_history", "route_to_agent")
        self.workflow.add_edge("route_to_agent", "execute_task")
        self.workflow.add_edge("execute_task", "update_memory")
        self.workflow.add_edge("update_memory", END)

        # 编译工作流
        self.agent = self.workflow.compile(
            checkpointer=self.checkpointer,
            store=self.memory_store
        )

    def _analyze_task(self, state: ContextualAgentState) -> Dict[str, Any]:
        """任务分析节点 - 理解用户意图"""
        messages = state.get("messages", [])
        if not messages:
            return state

        last_message = messages[-1]
        user_input = last_message.content if hasattr(last_message, 'content') else str(last_message)

        # 使用LLM分析任务类型
        analysis_prompt = f"""
        分析以下用户请求，确定任务类型和关键信息：

        用户请求: {user_input}

        请返回JSON格式：
        {{
            "task_type": "research|math|code|general",
            "current_task": "任务描述",
            "key_concepts": ["关键概念1", "关键概念2"]
        }}
        """

        try:
            response = self.llm.invoke(analysis_prompt)
            # 简化处理，实际应该解析JSON
            task_type = "general"
            if "数学" in user_input or "计算" in user_input or any(op in user_input for op in ["+", "-", "*", "/", "sin", "cos"]):
                task_type = "math"
            elif "搜索" in user_input or "查询" in user_input or "什么是" in user_input:
                task_type = "research"
            elif "代码" in user_input or "编程" in user_input or "python" in user_input.lower():
                task_type = "code"

            return {
                **state,
                "current_task": user_input,
                "task_type": task_type,
                "working_memory": {"analysis_complete": True}
            }
        except Exception as e:
            self.console.print(f"[red]任务分析失败: {e}[/red]")
            return {
                **state,
                "current_task": user_input,
                "task_type": "general"
            }

    def _select_context(self, state: ContextualAgentState, store: BaseStore) -> Dict[str, Any]:
        """上下文选择节点 - Select技术"""
        task_type = state.get("task_type", "general")
        current_task = state.get("current_task", "")

        # 1. 从长期记忆中选择相关信息
        namespace = ("agent", "memory")
        relevant_memories = []
        try:
            memory_items = list(store.search(namespace))
            for item in memory_items[-5:]:  # 获取最近5条记忆
                relevant_memories.append(item.value)
        except Exception:
            pass

        # 2. RAG知识检索
        retrieved_knowledge = []
        if self.retriever_tool and task_type == "research":
            try:
                retrieval_result = self.retriever_tool.invoke({"query": current_task})
                retrieved_knowledge = [str(retrieval_result)]
            except Exception:
                pass

        # 3. 工具选择 (BigTool技术)
        selected_tools = []
        if task_type == "math":
            # 使用语义搜索选择相关数学工具
            try:
                tool_results = list(self.tool_store.search(("tools",), query=current_task, limit=3))
                selected_tools = [result.value for result in tool_results]
            except Exception:
                pass

        return {
            **state,
            "retrieved_knowledge": retrieved_knowledge,
            "working_memory": {
                **state.get("working_memory", {}),
                "relevant_memories": relevant_memories,
                "selected_tools": selected_tools
            }
        }

    def _compress_history(self, state: ContextualAgentState) -> Dict[str, Any]:
        """历史压缩节点 - Compress技术"""
        messages = state.get("messages", [])

        # 如果消息太多，进行压缩
        if len(messages) > 10:
            try:
                # 提取关键信息进行总结
                recent_messages = messages[-5:]  # 保留最近5条
                older_messages = messages[:-5]

                # 总结旧消息
                summary_prompt = f"""
                请总结以下对话历史的关键信息：
                {[msg.content if hasattr(msg, 'content') else str(msg) for msg in older_messages]}

                提供简洁的总结，保留重要信息。
                """

                summary_response = self.llm.invoke(summary_prompt)
                compressed_history = summary_response.content

                return {
                    **state,
                    "compressed_history": compressed_history,
                    "messages": recent_messages  # 只保留最近的消息
                }
            except Exception:
                pass

        return state

    def _route_to_agent(self, state: ContextualAgentState) -> Dict[str, Any]:
        """代理路由节点 - Isolate技术"""
        task_type = state.get("task_type", "general")

        # 根据任务类型分配专门的代理
        agent_mapping = {
            "math": "math_expert",
            "research": "research_expert",
            "code": "code_expert",
            "general": "general"
        }

        assigned_agent = agent_mapping.get(task_type, "general")

        return {
            **state,
            "assigned_agent": assigned_agent
        }

    def _execute_task(self, state: ContextualAgentState) -> Dict[str, Any]:
        """任务执行节点 - 使用选定的代理执行任务"""
        assigned_agent = state.get("assigned_agent", "general")
        current_task = state.get("current_task", "")
        messages = state.get("messages", [])

        try:
            if assigned_agent == "general" or not self.supervisor_workflow:
                # 使用主LLM处理一般任务
                context_info = ""

                # 添加压缩历史
                if state.get("compressed_history"):
                    context_info += f"历史总结: {state['compressed_history']}\n"

                # 添加检索知识
                if state.get("retrieved_knowledge"):
                    context_info += f"相关知识: {state['retrieved_knowledge']}\n"

                prompt = f"""
                {context_info}

                用户请求: {current_task}

                请提供有帮助的回答。
                """

                response = self.llm.invoke(prompt)
                result_message = AIMessage(content=response.content)

            else:
                # 使用专门的子代理
                if assigned_agent == "math_expert":
                    agent_result = self.math_agent.invoke({"messages": [HumanMessage(content=current_task)]})
                elif assigned_agent == "research_expert":
                    agent_result = self.research_agent.invoke({"messages": [HumanMessage(content=current_task)]})
                elif assigned_agent == "code_expert":
                    agent_result = self.code_agent.invoke({"messages": [HumanMessage(content=current_task)]})
                else:
                    # 使用监督者工作流
                    agent_result = self.supervisor_workflow.invoke({"messages": [{"role": "user", "content": current_task}]})

                # 提取结果
                if isinstance(agent_result, dict) and "messages" in agent_result:
                    result_message = agent_result["messages"][-1]
                else:
                    result_message = AIMessage(content=str(agent_result))

            # 更新消息历史
            updated_messages = messages + [result_message]

            return {
                **state,
                "messages": updated_messages,
                "working_memory": {
                    **state.get("working_memory", {}),
                    "execution_complete": True,
                    "used_agent": assigned_agent
                }
            }

        except Exception as e:
            self.console.print(f"[red]任务执行失败: {e}[/red]")
            error_message = AIMessage(content=f"抱歉，执行任务时出现错误: {str(e)}")
            return {
                **state,
                "messages": messages + [error_message]
            }

    def _update_memory(self, state: ContextualAgentState, store: BaseStore) -> Dict[str, Any]:
        """更新记忆节点 - Write技术"""
        try:
            # 保存当前对话到长期记忆
            namespace = ("agent", "memory")
            memory_key = f"conversation_{uuid.uuid4()}"

            memory_data = {
                "task": state.get("current_task", ""),
                "task_type": state.get("task_type", ""),
                "agent_used": state.get("assigned_agent", ""),
                "timestamp": str(uuid.uuid4()),  # 简化时间戳
                "summary": state.get("compressed_history", "")
            }

            store.put(namespace, memory_key, memory_data)

            # 更新工具使用历史
            tool_usage = state.get("tool_usage_history", [])
            if state.get("working_memory", {}).get("used_agent"):
                tool_usage.append({
                    "agent": state["working_memory"]["used_agent"],
                    "task": state.get("current_task", "")
                })

            return {
                **state,
                "tool_usage_history": tool_usage,
                "working_memory": {
                    **state.get("working_memory", {}),
                    "memory_updated": True
                }
            }

        except Exception as e:
            self.console.print(f"[yellow]记忆更新失败: {e}[/yellow]")
            return state

    def chat(self, user_input: str, thread_id: str = "default") -> str:
        """主要聊天接口"""
        try:
            # 配置会话
            config = {"configurable": {"thread_id": thread_id}}

            # 创建初始状态
            initial_state = {
                "messages": [HumanMessage(content=user_input)],
                "current_task": "",
                "task_type": "",
                "working_memory": {},
                "compressed_history": "",
                "retrieved_knowledge": [],
                "tool_usage_history": [],
                "assigned_agent": None
            }

            # 执行工作流
            result = self.agent.invoke(initial_state, config)

            # 提取回复
            messages = result.get("messages", [])
            if messages:
                last_message = messages[-1]
                if hasattr(last_message, 'content'):
                    return last_message.content
                else:
                    return str(last_message)
            else:
                return "抱歉，我无法处理您的请求。"

        except Exception as e:
            self.console.print(f"[red]聊天处理失败: {e}[/red]")
            return f"抱歉，处理您的请求时出现错误: {str(e)}"

    def get_memory_summary(self, thread_id: str = "default") -> Dict[str, Any]:
        """获取记忆总结"""
        try:
            config = {"configurable": {"thread_id": thread_id}}
            state = self.agent.get_state(config)

            return {
                "current_state": state.values if state else {},
                "memory_items": list(self.memory_store.search(("agent", "memory")))[-5:],
                "tool_usage": state.values.get("tool_usage_history", []) if state else []
            }
        except Exception as e:
            return {"error": str(e)}

    def clear_memory(self, thread_id: str = "default"):
        """清除指定线程的记忆"""
        try:
            # 这里应该实现清除特定线程记忆的逻辑
            self.console.print(f"[green]已清除线程 {thread_id} 的记忆[/green]")
        except Exception as e:
            self.console.print(f"[red]清除记忆失败: {e}[/red]")

    def add_knowledge(self, content: str, title: str = ""):
        """添加知识到RAG系统"""
        try:
            from langchain_core.documents import Document
            doc = Document(page_content=content, metadata={"title": title})

            if self.knowledge_store:
                # 这里需要实现向现有向量存储添加文档的逻辑
                self.console.print(f"[green]已添加知识: {title}[/green]")
            else:
                self.console.print("[yellow]知识库未初始化[/yellow]")
        except Exception as e:
            self.console.print(f"[red]添加知识失败: {e}[/red]")


# 使用示例和测试函数
def main():
    """主函数 - 演示Agent的使用"""
    console = Console()

    console.print("[bold blue]初始化Contextual AI Agent...[/bold blue]")

    try:
        # 创建Agent实例
        agent = ContextualAIAgent()
        console.print("[green]✓ Agent初始化成功![/green]")

        # 测试不同类型的任务
        test_cases = [
            "什么是人工智能？",  # 研究任务
            "计算 sin(π/4) 的值",  # 数学任务
            "写一个计算斐波那契数列的Python函数",  # 代码任务
            "今天天气怎么样？"  # 一般任务
        ]

        for i, test_input in enumerate(test_cases):
            console.print(f"\n[bold cyan]测试 {i+1}: {test_input}[/bold cyan]")
            response = agent.chat(test_input, thread_id=f"test_{i}")
            console.print(f"[green]回复:[/green] {response}")

        # 显示记忆总结
        console.print("\n[bold magenta]记忆总结:[/bold magenta]")
        memory_summary = agent.get_memory_summary("test_0")
        pprint(memory_summary)

    except Exception as e:
        console.print(f"[red]初始化失败: {e}[/red]")
        console.print("[yellow]请确保已设置正确的API密钥[/yellow]")


if __name__ == "__main__":
    main()
