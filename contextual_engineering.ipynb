#%% md
<!-- omit in toc -->
# Lang<PERSON>hain AI Agents Using Contextual Engineering

Context engineering means creating the right setup for an AI before giving it a task. This setup includes:

*   **Instructions** on how the AI should act, like being a helpful budget travel guide
*   Access to **useful info** from databases, documents, or live sources.
*   Remembering **past conversations** to avoid repeats or forgetting.
*   **Tools** the AI can use, such as calculators or search features.
*   Important details about you, like your **preferences** or location.

![Context Engineering](https://cdn-images-1.medium.com/max/1500/1*sCTOzjG6KP7slQuxLZUtNg.png)
*Context Engineering (From [LangChain](https://blog.langchain.com/context-engineering-for-agents/) and [12Factor](https://github.com/humanlayer/12-factor-agents/tree/main))*

[AI engineers are now shifting](https://diamantai.substack.com/p/why-ai-experts-are-moving-from-prompt) from prompt engineering to context engineering because…

> context engineering focuses on providing AI with the right background and tools, making its answers smarter and more useful.

In this notebook, we will explore how **Lang<PERSON>hain** and **LangGraph**, two powerful tools for building AI agents, RAG apps, and LLM apps, can be used to implement **contextual engineering** effectively to improve our AI Agents.
#%% md
### Table of Contents
- [What is Context Engineering?](#what-is-context-engineering)
- [Writing Context: Scratchpad and Memory](#writing-context-scratchpad-and-memory)
- [Selecting Context: State, Memory, RAG, and Tools](#selecting-context-state-memory-rag-and-tools)
- [Compressing Context: Summarization Strategies](#compressing-context-summarization-strategies)
- [Isolating Context: Sub-Agents and Sandboxing](#isolating-context-sub-agents-and-sandboxing)
- [Summarizing Everything](#summarizing-everything)
#%% md
### What is Context Engineering?
LLMs work like a new type of operating system. The LLM acts like the CPU, and its context window works like RAM, serving as its short-term memory. But, like RAM, the context window has limited space for different information.

> Just as an operating system decides what goes into RAM, “context engineering” is about choosing what the LLM should keep in its context.

![Different Context Types](https://cdn-images-1.medium.com/max/1000/1*kMEQSslFkhLiuJS8-WEMIg.png)

When building LLM applications, we need to manage different types of context. Context engineering covers these main types:

*   Instructions: prompts, examples, memories, and tool descriptions
*   Knowledge: facts, stored information, and memories
*   Tools: feedback and results from tool calls

This year, more people are interested in agents because LLMs are better at thinking and using tools. Agents work on long tasks by using LLMs and tools together, choosing the next step based on the tool’s feedback.

![Agent Workflow](https://cdn-images-1.medium.com/max/1500/1*Do44CZkpPYyIJefuNQ69GA.png)

But long tasks and collecting too much feedback from tools use a lot of tokens. This can create problems: the context window can overflow, costs and delays can increase, and the agent might work worse.

Drew Breunig explained how too much context can hurt performance, including:

*   Context Poisoning: [when a mistake or hallucination gets added to the context](https://www.dbreunig.com/2025/06/22/how-contexts-fail-and-how-to-fix-them.html?ref=blog.langchain.com#context-poisoning)
*   Context Distraction: [when too much context confuses the model](https://www.dbreunig.com/2025/06/22/how-contexts-fail-and-how-to-fix-them.html?ref=blog.langchain.com#context-distraction)
*   Context Confusion: [when extra, unnecessary details affect the answer](https://www.dbreunig.com/2025/06/22/how-contexts-fail-and-how-to-fix-them.html?ref=blog.langchain.com#context-confusion)
*   Context Clash: [when parts of the context give conflicting information](https://www.dbreunig.com/2025/06/22/how-contexts-fail-and-how-to-fix-them.html?ref=blog.langchain.com#context-clash)

![Multiple turns in Agent](https://cdn-images-1.medium.com/max/1500/1*ZJeZJPKI5jC_1BMCoghZxA.png)

Anthropic [in their research](https://www.anthropic.com/engineering/built-multi-agent-research-system?ref=blog.langchain.com) stressed the need for it:

> Agents often have conversations with hundreds of turns, so managing context carefully is crucial.

So, how are people solving this problem today? Common strategies for agent context engineering can be grouped into four main types:

*   **Write**: creating clear and useful context
*   **Select**: picking only the most relevant information
*   **Compress**: shortening context to save space
*   **Isolate**: keeping different types of context separate

![Categories of Context Engineering](https://cdn-images-1.medium.com/max/2600/1*CacnXVAI6wR4eSIWgnZ9sg.png)
*Categories of Context Engineering (From [LangChain docs](https://blog.langchain.com/context-engineering-for-agents/))*

[LangGraph](https://www.langchain.com/langgraph) is built to support all these strategies. We will go through each of these components one by one and see how they help make our AI agents work better.
#%% md
### Writing Context: Scratchpad and Memory

The first principle of contextual engineering is **writing** context. This means creating and storing information outside the LLM's immediate context window, which the agent can access later. We will explore two primary mechanisms for this in LangGraph: the **scratchpad** (for short-term, session-specific notes) and **memory** (for long-term persistence across sessions).

![First Component of CE](https://cdn-images-1.medium.com/max/1000/1*aXpKxYt03iZPcrGkxsFvrQ.png)
#%% md
#### Scratchpad with LangGraph
Just like humans take notes to remember things for later tasks, agents can do the same using a [scratchpad](https://www.anthropic.com/engineering/claude-think-tool). It stores information outside the context window so the agent can access it whenever needed.

A good example is [Anthropic's multi-agent researcher](https://www.anthropic.com/engineering/built-multi-agent-research-system):

> *The LeadResearcher plans its approach and saves it to memory, because if the context window goes beyond 200,000 tokens, it gets cut off so saving the plan ensures it isn’t lost.*

In LangGraph, the `StateGraph` object serves as this scratchpad. The state is the central data structure passed between nodes in your graph. You define its schema, and each node can read from and write to it. This provides a powerful way to maintain short-term, thread-scoped memory for your agent.

First, let's set up our environment and helper utilities for printing.
#%%
# Import necessary libraries for typing, formatting, and environment management
import getpass
import os
from typing import TypedDict

from IPython.display import Image, display
from rich.console import Console
from rich.pretty import pprint

# Initialize a console for rich, formatted output in the notebook.
console = Console()

# Set the Anthropic API key to authenticate requests
# It's recommended to set this as an environment variable for security
if "ANTHROPIC_API_KEY" not in os.environ:
    os.environ["ANTHROPIC_API_KEY"] = getpass.getpass("Provide your Anthropic API key: ")
#%% md
Next, we will create a `TypedDict` for the state object. This defines the schema of our scratchpad, ensuring data consistency as it flows through the graph.
#%%
# Define the schema for the graph's state using TypedDict.
# This class acts as a data structure that will be passed between nodes in the graph.
# It ensures that the state has a consistent shape and provides type hints.
class State(TypedDict):
    """
    Defines the structure of the state for our joke generator workflow.

    Attributes:
        topic: The input topic for which a joke will be generated.
        joke: The output field where the generated joke will be stored.
    """

    topic: str
    joke: str
#%% md
#### Creating a StateGraph to Write to the Scratchpad
Once we define a state object, we can write context to it using a `StateGraph`. A StateGraph is LangGraph’s main tool for building stateful agents.

- **Nodes** are steps in the workflow. Each node is a function that takes the current state as input and returns updates.
- **Edges** connect nodes, defining the execution flow.

Let's create a chat model and a node function that uses it to generate a joke and write it to our state object.
#%%
# Import necessary libraries for LangChain and LangGraph
from langchain.chat_models import init_chat_model
from langgraph.graph import END, START, StateGraph

# --- Model Setup ---
# Initialize the chat model to be used in the workflow
# We use a specific Claude model with temperature=0 for deterministic outputs
llm = init_chat_model("anthropic:claude-3-sonnet-20240229", temperature=0)

# --- Define Workflow Node ---
def generate_joke(state: State) -> dict[str, str]:
    """
    A node function that generates a joke based on the topic in the current state.

    This function reads the 'topic' from the state, uses the LLM to generate a joke,
    and returns a dictionary to update the 'joke' field in the state.

    Args:
        state: The current state of the graph, which must contain a 'topic'.

    Returns:
        A dictionary with the 'joke' key to update the state.
    """
    # Read the topic from the state
    topic = state["topic"]
    print(f"Generating a joke about: {topic}")

    # Invoke the language model to generate a joke
    msg = llm.invoke(f"Write a short joke about {topic}")

    # Return the generated joke to be written back to the state
    return {"joke": msg.content}

# --- Build and Compile the Graph ---
# Initialize a new StateGraph with the predefined State schema
workflow = StateGraph(State)

# Add the 'generate_joke' function as a node in the graph
workflow.add_node("generate_joke", generate_joke)

# Define the workflow's execution path:
# The graph starts at the START entrypoint and flows to our 'generate_joke' node.
workflow.add_edge(START, "generate_joke")
# After 'generate_joke' completes, the graph execution ends.
workflow.add_edge("generate_joke", END)

# Compile the workflow into an executable chain
chain = workflow.compile()

# --- Visualize the Graph ---
# Display a visual representation of the compiled workflow graph
display(Image(chain.get_graph().draw_mermaid_png()))
#%% md
Now we can execute this workflow. It will take an initial state with a `topic`, run the `generate_joke` node, and write the result into the `joke` field of the state.
#%%
# --- Execute the Workflow ---
# Invoke the compiled graph with an initial state containing the topic.
# The `invoke` method runs the graph from the START node to the END node.
joke_generator_state = chain.invoke({"topic": "cats"})

# --- Display the Final State ---
# Print the final state of the graph after execution.
# This will show both the input 'topic' and the output 'joke' that was written to the state.
console.print("\n[bold blue]Joke Generator Final State:[/bold blue]")
pprint(joke_generator_state)
#%% md
#### Memory Writing in LangGraph
Scratchpads help agents work within a single session, but sometimes agents need to remember things across multiple sessions. This is where long-term memory comes in.

*   [Reflexion](https://arxiv.org/abs/2303.11366) introduced the idea of agents reflecting after each turn and reusing self-generated hints.
*   [Generative Agents](https://ar5iv.labs.arxiv.org/html/2304.03442) created long-term memories by summarizing past agent feedback.

![Memory Writing](https://cdn-images-1.medium.com/max/1000/1*VaMVevdSVxDITLK1j0LfRQ.png)

LangGraph supports long-term memory through a `store` that can be passed to a compiled graph. This allows you to persist context *across threads* (e.g., different chat sessions).

- **Checkpointing** saves the graph’s state at each step in a `thread`.
- **Long-term memory** lets you keep specific context across threads using a key-value `BaseStore`.

Let's enhance our agent to use both short-term checkpointing and a long-term memory store.
#%%
# Import memory and persistence components from LangGraph
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.store.base import BaseStore
from langgraph.store.memory import InMemoryStore

# Initialize storage components
checkpointer = InMemorySaver()  # For thread-level state persistence (short-term memory)
memory_store = InMemoryStore()  # For cross-thread memory storage (long-term memory)

# Define a namespace to logically group related data in the long-term store.
namespace = ("rlm", "joke_generator")

def generate_joke_with_memory(state: State, store: BaseStore) -> dict[str, str]:
    """Generate a joke with memory awareness.
    
    This enhanced version checks for existing jokes in long-term memory
    before generating a new one and saves the new joke.
    
    Args:
        state: Current state containing the topic.
        store: Memory store for persistent context.
        
    Returns:
        A dictionary with the generated joke.
    """
    # Check if there's an existing joke in memory (we will cover selection later)
    existing_jokes = list(store.search(namespace))
    if existing_jokes:
        existing_joke_content = existing_jokes[0].value
        print(f"Found existing joke in memory: {existing_joke_content}")
    else:
        print("No existing joke found in memory.")

    # Generate a new joke based on the topic
    msg = llm.invoke(f"Write a short joke about {state['topic']}")
    
    # Write the new joke to long-term memory
    store.put(namespace, "last_joke", {"joke": msg.content})
    print(f"Wrote new joke to memory: {msg.content[:50]}...")

    # Return the joke to be added to the current session's state (scratchpad)
    return {"joke": msg.content}


# Build the workflow with memory capabilities
workflow_with_memory = StateGraph(State)
workflow_with_memory.add_node("generate_joke", generate_joke_with_memory)
workflow_with_memory.add_edge(START, "generate_joke")
workflow_with_memory.add_edge("generate_joke", END)

# Compile with both checkpointing (for session state) and a memory store (for long-term)
chain_with_memory = workflow_with_memory.compile(checkpointer=checkpointer, store=memory_store)
#%% md
Now, let's execute the updated workflow. We'll use a `config` object to specify a `thread_id`. This identifies the current session. The first time we run it, there should be no joke in long-term memory.
#%%
# Execute the workflow within a specific thread (e.g., a user session)
config_thread_1 = {"configurable": {"thread_id": "1"}}
joke_state_thread_1 = chain_with_memory.invoke({"topic": "dogs"}, config_thread_1)

# Display the workflow result for the first thread
console.print("\n[bold cyan]Workflow Result (Thread 1):[/bold cyan]")
pprint(joke_state_thread_1)
#%% md
Because we compiled the workflow with a checkpointer, we can now view the latest state of the graph for that thread. This shows the value of the short-term scratchpad.
#%%
# --- Retrieve and Inspect the Graph State ---
# Use the `get_state` method to retrieve the latest state snapshot for thread "1".
latest_state_thread_1 = chain_with_memory.get_state(config_thread_1)

# --- Display the State Snapshot ---
# The StateSnapshot includes not only the data ('topic', 'joke') but also execution metadata.
console.print("\n[bold magenta]Latest Graph State (Thread 1):[/bold magenta]")
pprint(latest_state_thread_1)
#%% md
Now, let's run the workflow again but with a *different* `thread_id`. This simulates a new session. Our long-term memory store should now contain the joke from the first session, demonstrating how context can be persisted and shared across threads.
#%%
# Execute the workflow with a different thread ID to simulate a new session
config_thread_2 = {"configurable": {"thread_id": "2"}}
joke_state_thread_2 = chain_with_memory.invoke({"topic": "birds"}, config_thread_2)

# Display the result, which should show that it found the joke from the previous thread in memory
console.print("\n[bold yellow]Workflow Result (Thread 2):[/bold yellow]")
pprint(joke_state_thread_2)
#%% md
### Selecting Context: State, Memory, RAG, and Tools

The second principle is **selecting** context. Once context is written, agents need to be able to retrieve the *most relevant* pieces of information for the current task. This prevents context window overflow and keeps the agent focused.

![Second Component of CE](https://cdn-images-1.medium.com/max/1000/1*VZiHtQ_8AlNdV3HIMrbBZA.png)

We will explore four ways to select context:
1.  **From the Scratchpad (State):** Selecting data written in the current session.
2.  **From Long-Term Memory:** Retrieving data from past sessions.
3.  **From Knowledge (RAG):** Using Retrieval-Augmented Generation to fetch information from documents.
4.  **From Tools (Tool-RAG):** Using RAG to select the best tool for a job.
#%% md
#### Scratchpad Selection Approach
How you select context from a scratchpad depends on its implementation. Since our scratchpad is the agent's runtime `State` object, we (the developer) decide which parts of the state to share with the agent at each step. This gives fine-grained control.

Let's create a two-step workflow. The first node generates a joke (writes to state). The second node *selects* that joke from the state and improves it.
#%%
# We need a state that can hold the original and the improved joke
class JokeImprovementState(TypedDict):
    topic: str
    joke: str
    improved_joke: str

def improve_joke(state: JokeImprovementState) -> dict[str, str]:
    """Improve an existing joke by adding wordplay.
    
    This demonstrates selecting context from state - we read the existing
    joke from state and use it to generate an improved version.
    
    Args:
        state: Current state containing the original joke.
        
    Returns:
        A dictionary with the improved joke.
    """
    initial_joke = state["joke"]
    print(f"Initial joke selected from state: {initial_joke[:50]}...")
    
    # Select the joke from state to present it to the LLM
    msg = llm.invoke(f"Make this joke funnier by adding wordplay: {initial_joke}")
    return {"improved_joke": msg.content}

# --- Build the two-step workflow ---
selection_workflow = StateGraph(JokeImprovementState)

# Add the initial joke generation node (reusing from before)
selection_workflow.add_node("generate_joke", generate_joke)
# Add the new improvement node
selection_workflow.add_node("improve_joke", improve_joke)

# Connect nodes in sequence
selection_workflow.add_edge(START, "generate_joke")
selection_workflow.add_edge("generate_joke", "improve_joke")
selection_workflow.add_edge("improve_joke", END)

# Compile the workflow
selection_chain = selection_workflow.compile()

# Visualize the new graph
display(Image(selection_chain.get_graph().draw_mermaid_png()))
#%%
# Execute the workflow to see context selection in action
joke_improvement_state = selection_chain.invoke({"topic": "computers"})

# Display the final state with rich formatting
console.print("\n[bold blue]Final Joke Improvement State:[/bold blue]")
pprint(joke_improvement_state)
#%% md
#### Memory Selection Ability
If agents can save memories, they also need to select relevant memories for the task at hand. This is useful for recalling:
- **Episodic memories:** Few-shot examples of desired behavior.
- **Procedural memories:** Instructions to guide behavior.
- **Semantic memories:** Facts or relationships for task-relevant context.

In our previous example, we wrote to the `InMemoryStore`. Now, we can select context from it using the `store.get()` method to pull relevant state into our workflow. Let's create a node that selects the previously stored joke and tries to generate a *different* one.
#%%
# Re-initialize storage components for this example
checkpointer_select = InMemorySaver()
memory_store_select = InMemoryStore()
# Pre-populate the store with a joke for selection
memory_store_select.put(namespace, "last_joke", {"joke": "Why was the computer cold? Because it left its Windows open!"})

def generate_different_joke(state: State, store: BaseStore) -> dict[str, str]:
    """Generate a joke with memory-aware context selection.
    
    This function demonstrates selecting context from memory before
    generating new content, ensuring it doesn't repeat itself.
    
    Args:
        state: Current state containing the topic
        store: Memory store for persistent context
        
    Returns:
        Dictionary with the newly generated joke
    """
    # Select prior joke from memory if it exists
    prior_joke_item = store.get(namespace, "last_joke")
    prior_joke_text = "None"
    if prior_joke_item:
        prior_joke_text = prior_joke_item.value["joke"]
        print(f"Selected prior joke from memory: {prior_joke_text}")
    else:
        print("No prior joke found in memory.")

    # Generate a new joke that differs from the prior one
    prompt = (
        f"Write a short joke about {state['topic']}, "
        f"but make it different from this prior joke: '{prior_joke_text}'"
    )
    msg = llm.invoke(prompt)

    # Store the new joke in memory for future context selection
    store.put(namespace, "last_joke", {"joke": msg.content})

    return {"joke": msg.content}

# Build the memory-aware workflow
memory_selection_workflow = StateGraph(State)
memory_selection_workflow.add_node("generate_joke", generate_different_joke)
memory_selection_workflow.add_edge(START, "generate_joke")
memory_selection_workflow.add_edge("generate_joke", END)

# Compile with both checkpointing and memory store
memory_selection_chain = memory_selection_workflow.compile(checkpointer=checkpointer_select, store=memory_store_select)

# Execute the workflow
config = {"configurable": {"thread_id": "3"}}
new_joke_state = memory_selection_chain.invoke({"topic": "computers"}, config)

console.print("\n[bold green]Memory Selection Workflow Final State:[/bold green]")
pprint(new_joke_state)
#%% md
#### Advantage of LangGraph BigTool Calling (Tool Selection)
Agents use tools, but giving them too many can cause confusion, especially when tool descriptions overlap. A solution is to use RAG on tool descriptions to fetch only the most relevant tools for a task.

> According to [recent research](https://arxiv.org/abs/2505.03275), this improves tool selection accuracy by up to 3x.

The `langgraph-bigtool` library is ideal for this. It applies semantic similarity search over tool descriptions to select the most relevant ones. Let’s demonstrate by creating an agent with all functions from Python’s built-in `math` library and see how it selects the correct one.
#%%
# Import necessary libraries for this example
import math
import types
import uuid

from langchain.embeddings import init_embeddings
from langgraph_bigtool import create_agent
from langgraph_bigtool.utils import convert_positional_only_function_to_tool
from utils import format_messages # A helper from the provided utils.py

# Ensure OpenAI API key is set for embeddings
if "OPENAI_API_KEY" not in os.environ:
    os.environ["OPENAI_API_KEY"] = getpass.getpass("Provide your OpenAI API key: ")

# --- 1. Collect and Prepare Tools ---
# Collect all built-in functions from the `math` module
all_math_tools = []
for function_name in dir(math):
    function = getattr(math, function_name)
    if isinstance(function, types.BuiltinFunctionType):
        # This handles an idiosyncrasy of the `math` library's function signatures
        if tool := convert_positional_only_function_to_tool(function):
            all_math_tools.append(tool)

# Create a registry mapping unique IDs to each tool instance
tool_registry = {str(uuid.uuid4()): tool for tool in all_math_tools}

# --- 2. Index Tools for Semantic Search ---
# Initialize the embeddings model
embeddings = init_embeddings("openai:text-embedding-3-small")

# Set up an in-memory store configured for vector search on tool descriptions
tool_store = InMemoryStore(
    index={
        "embed": embeddings,
        "dims": 1536, # Dimension for text-embedding-3-small
        "fields": ["description"],
    }
)

# Index each tool's name and description into the store
for tool_id, tool in tool_registry.items():
    tool_store.put(
        ("tools",), # A namespace for tools
        tool_id,
        {"description": f"{tool.name}: {tool.description}"},
    )

# --- 3. Create and Compile the Agent ---
# The create_agent function from langgraph-bigtool sets up the agent logic
builder = create_agent(llm, tool_registry)
bigtool_agent = builder.compile(store=tool_store)

display(Image(bigtool_agent.get_graph().draw_mermaid_png()))
#%%
# --- 4. Invoke the Agent ---
# Define the query for the agent. This requires selecting the correct math tool.
query = "Use available tools to calculate arc cosine of 0.5."

# Invoke the agent. It will first search its tools, select 'acos', and then execute it.
result = bigtool_agent.invoke({"messages": query})

# Format and display the final messages from the agent's execution.
# The output will show the agent's thought process: searching, finding, and using the tool.
format_messages(result['messages'])
#%% md
#### RAG with Contextual Engineering (Knowledge Selection)
[RAG (Retrieval-Augmented Generation)](https://github.com/langchain-ai/rag-from-scratch) is a cornerstone of context engineering. It allows agents to select relevant knowledge from vast document stores.

In LangGraph, this is typically done by creating a retrieval tool. Let's build a RAG agent that can answer questions about Lilian Weng’s blog posts.
#%%
# Import necessary components for RAG
from langchain_community.document_loaders import WebBaseLoader
from langchain_core.vectorstores import InMemoryVectorStore
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain.tools.retriever import create_retriever_tool
from langgraph.graph import MessagesState
from langchain_core.messages import SystemMessage, ToolMessage
from typing_extensions import Literal

# --- 1. Load and Chunk Documents ---
# Define the URLs for Lilian Weng's blog posts
urls = [
    "https://lilianweng.github.io/posts/2025-05-01-thinking/",
    "https://lilianweng.github.io/posts/2024-11-28-reward-hacking/",
    "https://lilianweng.github.io/posts/2024-07-07-hallucination/",
    "https://lilianweng.github.io/posts/2024-04-12-diffusion-video/",
]
docs = [WebBaseLoader(url).load() for url in urls]
docs_list = [item for sublist in docs for item in sublist]

# Split the documents into smaller chunks for effective retrieval
text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
    chunk_size=2000, chunk_overlap=50
)
doc_splits = text_splitter.split_documents(docs_list)

# --- 2. Create Vector Store and Retriever Tool ---
vectorstore = InMemoryVectorStore.from_documents(documents=doc_splits, embedding=embeddings)
retriever = vectorstore.as_retriever()

# Create a retriever tool that the agent can call
retriever_tool = create_retriever_tool(
    retriever,
    "retrieve_blog_posts",
    "Search and return information about Lilian Weng blog posts.",
)

rag_tools = [retriever_tool]
rag_tools_by_name = {tool.name: tool for tool in rag_tools}
llm_with_rag_tools = llm.bind_tools(rag_tools)
#%% md
Now we define the graph components for our RAG agent: the prompt, the nodes for calling the LLM and the tool, and a conditional edge to create a loop.
#%%
# --- 3. Define the RAG Agent Graph ---
rag_prompt = """You are a helpful assistant tasked with retrieving information from a series of technical blog posts by Lilian Weng. 
Clarify the scope of research with the user before using your retrieval tool to gather context. Reflect on any context you fetch, and
proceed until you have sufficient context to answer the user's research request."""

def rag_llm_call(state: MessagesState):
    """Node to call the LLM. The LLM decides whether to call a tool or generate a final answer."""
    messages_with_prompt = [SystemMessage(content=rag_prompt)] + state["messages"]
    response = llm_with_rag_tools.invoke(messages_with_prompt)
    return {"messages": [response]}

def rag_tool_node(state: dict):
    """Node to perform the tool call and return the observation."""
    last_message = state["messages"][-1]
    result = []
    for tool_call in last_message.tool_calls:
        tool = rag_tools_by_name[tool_call["name"]]
        observation = tool.invoke(tool_call["args"])
        result.append(ToolMessage(content=str(observation), tool_call_id=tool_call["id"]))
    return {"messages": result}

def should_continue_rag(state: MessagesState) -> Literal["Action", END]:
    """Conditional edge to decide the next step. If the LLM made a tool call, route to the tool node. Otherwise, end."""
    if state["messages"][-1].tool_calls:
        return "Action"
    return END

# Build the RAG agent workflow
rag_agent_builder = StateGraph(MessagesState)
rag_agent_builder.add_node("llm_call", rag_llm_call)
rag_agent_builder.add_node("Action", rag_tool_node)
rag_agent_builder.set_entry_point("llm_call")
rag_agent_builder.add_conditional_edges("llm_call", should_continue_rag, {"Action": "Action", END: END})
rag_agent_builder.add_edge("Action", "llm_call")

rag_agent = rag_agent_builder.compile()
display(Image(rag_agent.get_graph(xray=True).draw_mermaid_png()))
#%%
# --- 4. Invoke the RAG Agent ---
query = "What are the types of reward hacking discussed in the blogs?"
result = rag_agent.invoke({"messages": [("user", query)]})
format_messages(result['messages'])
#%% md
### Compressing Context: Summarization Strategies

The third principle is **compressing** context. Agent interactions can span hundreds of turns and involve token-heavy tool calls. Summarization is a common and effective way to manage this, reducing token count while retaining essential information.

![Third Component of CE](https://cdn-images-1.medium.com/max/1000/1*Xu76qgF1u2G3JipeIgHo5Q.png)

We can add summarization at different points in the agent's workflow:
- At the end of a conversation to create a summary of the entire interaction.
- After a token-heavy tool call to compress its output before it enters the agent's scratchpad.

Let's explore both approaches.
#%% md
#### Approach 1: Summarizing the Entire Conversation

First, we'll build an agent that performs its RAG task and then, as a final step, generates a summary of the whole interaction. This can be useful for logging or creating a concise record of the agent's work.
#%%
from rich.markdown import Markdown

# Define an extended state that includes a summary field
class StateWithSummary(MessagesState):
    summary: str

summarization_prompt = """Summarize the full chat history and all tool feedback to give an overview of what the user asked about and what the agent did."""

def summary_node(state: MessagesState) -> dict:
    """Node to generate a summary of the conversation."""
    messages = [SystemMessage(content=summarization_prompt)] + state["messages"]
    result = llm.invoke(messages)
    return {"summary": result.content}

def should_continue_to_summary(state: MessagesState) -> Literal["Action", "summary_node"]:
    """Conditional edge to route to tool action or to the final summary node."""
    if state["messages"][-1].tool_calls:
        return "Action"
    return "summary_node"

# Build the workflow with a final summary step
summary_agent_builder = StateGraph(StateWithSummary)
summary_agent_builder.add_node("llm_call", rag_llm_call)
summary_agent_builder.add_node("Action", rag_tool_node)
summary_agent_builder.add_node("summary_node", summary_node)
summary_agent_builder.set_entry_point("llm_call")
summary_agent_builder.add_conditional_edges("llm_call", should_continue_to_summary, {"Action": "Action", "summary_node": "summary_node"})
summary_agent_builder.add_edge("Action", "llm_call")
summary_agent_builder.add_edge("summary_node", END)

summary_agent = summary_agent_builder.compile()
display(Image(summary_agent.get_graph(xray=True).draw_mermaid_png()))
#%%
# Run the agent and display the final summary
query = "Why does RL improve LLM reasoning according to the blogs?"
result = summary_agent.invoke({"messages": [("user", query)]})

console.print("\n[bold green]Final Agent Message:[/bold green]")
format_messages([result['messages'][-1]])

console.print("\n[bold purple]Generated Conversation Summary:[/bold purple]")
display(Markdown(result["summary"]))
#%% md
**Note:** While effective, this approach can be token-intensive, as the full, uncompressed tool outputs are passed through the loop. For the query above, this can use over 100k tokens.

#### Approach 2: Compressing Tool Outputs On-the-Fly
A more efficient approach is to compress the context *before* it enters the agent’s main scratchpad. Let’s update the RAG agent to summarize the tool call output immediately after it's received.
#%%
tool_summarization_prompt = """You will be provided a document from a RAG system.
Summarize the document, ensuring to retain all relevant and essential information.
Your goal is to reduce the size of the document (tokens) to a more manageable size for an agent."""

def tool_node_with_summarization(state: dict):
    """Performs the tool call and then immediately summarizes the output."""
    last_message = state["messages"][-1]
    result = []
    for tool_call in last_message.tool_calls:
        tool = rag_tools_by_name[tool_call["name"]]
        observation = tool.invoke(tool_call["args"])
        
        # Summarize the document before adding it to the state
        summary_msg = llm.invoke([
            SystemMessage(content=tool_summarization_prompt),
            ("user", str(observation))
        ])
        
        result.append(ToolMessage(content=summary_msg.content, tool_call_id=tool_call["id"]))
    return {"messages": result}

# Build the more efficient workflow
efficient_agent_builder = StateGraph(MessagesState)
efficient_agent_builder.add_node("llm_call", rag_llm_call)
efficient_agent_builder.add_node("Action", tool_node_with_summarization)
efficient_agent_builder.set_entry_point("llm_call")
efficient_agent_builder.add_conditional_edges("llm_call", should_continue_rag, {"Action": "Action", END: END})
efficient_agent_builder.add_edge("Action", "llm_call")

efficient_agent = efficient_agent_builder.compile()
display(Image(efficient_agent.get_graph(xray=True).draw_mermaid_png()))
#%%
# Run the same query with the efficient agent
query = "Why does RL improve LLM reasoning according to the blogs?"
result = efficient_agent.invoke({"messages": [("user", query)]})

console.print("\n[bold green]Efficient Agent Conversation Flow:[/bold green]")
format_messages(result['messages'])
#%% md
**Result:** This simple change can cut token usage by nearly half, making the agent far more efficient and cost-effective, demonstrating the power of on-the-fly context compression.
#%% md
### Isolating Context: Sub-Agents and Sandboxing
The final principle is **isolating** context. This involves splitting up the context to prevent different tasks or types of information from interfering with each other. This is crucial for complex, multi-step problems.

![Fourth Component of CE](https://cdn-images-1.medium.com/max/1000/1*-b9BLPkLHkYsy2iLQIdxUg.png)

We will look at two powerful isolation techniques:
1.  **Sub-Agent Architectures:** Using multiple, specialized agents managed by a supervisor.
2.  **Sandboxed Environments:** Executing code in a secure, isolated environment.
#%% md
#### Isolating Context using Sub-Agents Architecture

A common way to isolate context is by splitting tasks across sub-agents. OpenAI's [Swarm](https://github.com/openai/swarm) library was designed for this "separation of concerns," where each agent manages a specific sub-task with its own tools, instructions, and context window.

> *Subagents operate in parallel with their own context windows, exploring different aspects of the question simultaneously.* - Anthropic

LangGraph supports this through a **supervisor** architecture. The supervisor delegates tasks to specialized sub-agents, each running in its own isolated context window. Let’s build a supervisor that manages a `math_expert` and a `research_expert`.
#%%
# Import prebuilt agent creators
from langgraph.prebuilt import create_react_agent
from langgraph_supervisor import create_supervisor

# --- 1. Define Tools for Each Agent ---
def add(a: float, b: float) -> float:
    """Add two numbers."""
    return a + b

def multiply(a: float, b: float) -> float:
    """Multiply two numbers."""
    return a * b

def web_search(query: str) -> str:
    """Mock web search function that returns FAANG company headcounts."""
    return (
        "Here are the headcounts for each of the FAANG companies in 2024:\n"
        "1. **Facebook (Meta)**: 67,317 employees.\n"
        "2. **Apple**: 164,000 employees.\n"
        "3. **Amazon**: 1,551,000 employees.\n"
        "4. **Netflix**: 14,000 employees.\n"
        "5. **Google (Alphabet)**: 181,269 employees."
    )

# --- 2. Create Specialized Agents ---
# Each agent has its own tools and instructions, isolating its context
math_agent = create_react_agent(
    model=llm,
    tools=[add, multiply],
    name="math_expert",
    prompt="You are a math expert. Always use one tool at a time."
)

research_agent = create_react_agent(
    model=llm,
    tools=[web_search],
    name="research_expert",
    prompt="You are a world class researcher with access to web search. Do not do any math."
)

# --- 3. Create Supervisor Workflow ---
# The supervisor coordinates the agents
supervisor_workflow = create_supervisor(
    [research_agent, math_agent],
    model=llm,
    prompt=(
        "You are a team supervisor managing a research expert and a math expert. "
        "Delegate tasks to the appropriate agent to answer the user's query. "
        "For current events or facts, use research_agent. "
        "For math problems, use math_agent."
    )
)

# Compile the multi-agent application
multi_agent_app = supervisor_workflow.compile()
#%%
# --- 4. Execute the Multi-Agent Workflow ---
result = multi_agent_app.invoke({
    "messages": [
        {
            "role": "user",
            "content": "what's the combined headcount of the FAANG companies in 2024?"
        }
    ]
})

# Format and display the results, showing the delegation in action
format_messages(result['messages'])
#%% md
#### Isolation using Sandboxed Environments
Another powerful way to isolate context is to use a sandboxed execution environment. Instead of the LLM just calling tools via JSON, a `CodeAgent` can write and execute code in a secure sandbox. The results are then returned to the LLM.

This keeps heavy data or complex state (like variables in a script) outside the LLM’s token limit, isolating it in the environment.

The `langchain-sandbox` provides a secure environment for executing untrusted Python code using Pyodide (Python compiled to WebAssembly). We can add this as a tool to any LangGraph agent.

**Note:** Deno is required. Install it from: https://docs.deno.com/runtime/getting_started/installation/
#%%
# Import the sandbox tool and a prebuilt agent
from langchain_sandbox import PyodideSandboxTool
from langgraph.prebuilt import create_react_agent

# Create a sandbox tool. allow_net=True lets it install packages if needed.
sandbox_tool = PyodideSandboxTool(allow_net=True)

# Create a ReAct agent equipped with the sandbox tool
sandbox_agent = create_react_agent(llm, tools=[sandbox_tool])

# Execute a query that the agent can solve by writing and running Python code
result = await sandbox_agent.ainvoke(
    {"messages": [{"role": "user", "content": "what's 5 + 7?"}]},
)

# Format and display the results
format_messages(result['messages'])
#%% md
#### State Isolation in LangGraph
Finally, it's important to remember that the agent’s **runtime state object** is itself a powerful way to isolate context. By designing a state schema with different fields, you can control what the LLM sees.

For example, one field (like `messages`) can be shown to the LLM on each turn, while other fields store information (like raw tool outputs or intermediate calculations) that remains isolated until a specific node needs to access it. You’ve seen many examples of this throughout this notebook, where we explicitly read from and write to specific fields of the state object.
#%% md
### Summarizing Everything
Let’s summarize what we have done so far:

*   **Write:** We used LangGraph `StateGraph` to create a **"scratchpad"** for short-term memory and an `InMemoryStore` for long-term memory, allowing our agent to store and recall information.
*   **Select:** We demonstrated how to selectively pull relevant information from the agent’s state and long-term memory. This included using Retrieval-Augmented Generation (`RAG`) to find specific knowledge and `langgraph-bigtool` to select the right tool from many options.
*   **Compress:** To manage long conversations and token-heavy tool outputs, we implemented summarization. We showed how to compress `RAG` results on-the-fly to make the agent more efficient and reduce token usage.
*   **Isolate:** We explored keeping contexts separate to avoid confusion by building a multi-agent system with a supervisor that delegates tasks to specialized sub-agents and by using sandboxed environments to run code.

All these techniques fall under **“Contextual Engineering”** — a strategy to improve AI agents by carefully managing their working memory (`context`) to make them more efficient, accurate, and capable of handling complex, long-running tasks.