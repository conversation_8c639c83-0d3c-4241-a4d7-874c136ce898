#!/usr/bin/env python3
"""
Contextual AI Agent 演示脚本

这个脚本展示了如何使用集成所有上下文工程技术的AI Agent。
包含交互式聊天界面和功能演示。
"""

import os
import sys
from contextual_ai_agent import ContextualAIAgent
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt
from rich.table import Table
from rich.pretty import pprint

class AgentDemo:
    def __init__(self):
        self.console = Console()
        self.agent = None
        self.current_thread = "main"
        
    def setup_agent(self):
        """设置Agent"""
        self.console.print(Panel.fit(
            "[bold blue]Contextual AI Agent[/bold blue]\n"
            "集成上下文工程技术的智能助手\n\n"
            "[yellow]技术特性:[/yellow]\n"
            "• Write: StateGraph + InMemoryStore\n"
            "• Select: 状态选择 + 记忆选择 + RAG + BigTool\n"
            "• Compress: 对话总结 + RAG压缩\n"
            "• Isolate: 子代理架构 + 沙盒环境",
            title="🤖 AI Agent Demo"
        ))
        
        try:
            self.console.print("\n[yellow]正在初始化Agent...[/yellow]")
            self.agent = ContextualAIAgent()
            self.console.print("[green]✓ Agent初始化成功![/green]")
            return True
        except Exception as e:
            self.console.print(f"[red]✗ 初始化失败: {e}[/red]")
            self.console.print("\n[yellow]请确保已设置以下环境变量:[/yellow]")
            self.console.print("• ANTHROPIC_API_KEY")
            self.console.print("• OPENAI_API_KEY")
            return False
    
    def show_menu(self):
        """显示主菜单"""
        table = Table(title="🎯 功能菜单")
        table.add_column("选项", style="cyan", no_wrap=True)
        table.add_column("功能", style="white")
        table.add_column("技术展示", style="green")
        
        table.add_row("1", "智能聊天", "Write + Select + Compress + Isolate")
        table.add_row("2", "数学计算", "BigTool工具选择 + 数学专家代理")
        table.add_row("3", "知识查询", "RAG检索 + 研究专家代理")
        table.add_row("4", "代码执行", "沙盒环境 + 代码专家代理")
        table.add_row("5", "记忆管理", "长期记忆存储和检索")
        table.add_row("6", "技术演示", "展示所有上下文工程技术")
        table.add_row("0", "退出", "")
        
        self.console.print(table)
    
    def chat_mode(self):
        """聊天模式"""
        self.console.print("\n[bold cyan]💬 智能聊天模式[/bold cyan]")
        self.console.print("[dim]输入 'quit' 退出聊天模式[/dim]\n")
        
        while True:
            try:
                user_input = Prompt.ask("[bold blue]您[/bold blue]")
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    break
                    
                self.console.print("\n[yellow]🤔 思考中...[/yellow]")
                response = self.agent.chat(user_input, self.current_thread)
                
                self.console.print(Panel(
                    response,
                    title="🤖 AI Assistant",
                    border_style="green"
                ))
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                self.console.print(f"[red]错误: {e}[/red]")
    
    def math_demo(self):
        """数学计算演示"""
        self.console.print("\n[bold cyan]🔢 数学计算演示[/bold cyan]")
        
        math_examples = [
            "计算 sin(π/4) 的值",
            "求 log(100) 的值", 
            "计算 sqrt(16) 的结果",
            "求 factorial(5) 的值"
        ]
        
        for example in math_examples:
            self.console.print(f"\n[blue]示例: {example}[/blue]")
            response = self.agent.chat(example, "math_demo")
            self.console.print(f"[green]结果:[/green] {response}")
    
    def knowledge_demo(self):
        """知识查询演示"""
        self.console.print("\n[bold cyan]📚 知识查询演示[/bold cyan]")
        
        # 添加一些示例知识
        knowledge_items = [
            ("人工智能基础", "人工智能(AI)是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。"),
            ("机器学习", "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。"),
            ("深度学习", "深度学习是机器学习的一个分支，使用多层神经网络来模拟人脑的工作方式。")
        ]
        
        for title, content in knowledge_items:
            self.agent.add_knowledge(content, title)
        
        # 测试查询
        queries = [
            "什么是人工智能？",
            "机器学习和深度学习的区别是什么？",
            "解释一下神经网络的工作原理"
        ]
        
        for query in queries:
            self.console.print(f"\n[blue]查询: {query}[/blue]")
            response = self.agent.chat(query, "knowledge_demo")
            self.console.print(f"[green]回答:[/green] {response}")
    
    def code_demo(self):
        """代码执行演示"""
        self.console.print("\n[bold cyan]💻 代码执行演示[/bold cyan]")
        
        code_examples = [
            "写一个计算斐波那契数列的Python函数",
            "创建一个简单的排序算法",
            "计算1到100的和"
        ]
        
        for example in code_examples:
            self.console.print(f"\n[blue]任务: {example}[/blue]")
            response = self.agent.chat(example, "code_demo")
            self.console.print(f"[green]结果:[/green] {response}")
    
    def memory_demo(self):
        """记忆管理演示"""
        self.console.print("\n[bold cyan]🧠 记忆管理演示[/bold cyan]")
        
        # 显示当前记忆状态
        memory_summary = self.agent.get_memory_summary(self.current_thread)
        
        self.console.print("\n[yellow]当前记忆状态:[/yellow]")
        pprint(memory_summary)
        
        # 测试记忆功能
        self.console.print("\n[blue]测试记忆功能...[/blue]")
        self.agent.chat("请记住我的名字是张三", "memory_test")
        self.agent.chat("我喜欢编程和数学", "memory_test")
        
        # 查询记忆
        response = self.agent.chat("你还记得我的名字和爱好吗？", "memory_test")
        self.console.print(f"[green]记忆测试结果:[/green] {response}")
    
    def tech_demo(self):
        """技术演示"""
        self.console.print("\n[bold cyan]🔬 技术演示[/bold cyan]")
        
        demos = [
            ("Write技术", "展示状态管理和记忆写入"),
            ("Select技术", "展示上下文选择和RAG检索"),
            ("Compress技术", "展示对话历史压缩"),
            ("Isolate技术", "展示子代理架构")
        ]
        
        for tech, desc in demos:
            self.console.print(f"\n[bold yellow]{tech}:[/bold yellow] {desc}")
            
        # 综合演示
        complex_query = "我需要研究人工智能的发展历史，然后计算一些相关的统计数据，最后写代码来可视化结果"
        self.console.print(f"\n[blue]综合任务:[/blue] {complex_query}")
        response = self.agent.chat(complex_query, "tech_demo")
        self.console.print(f"[green]处理结果:[/green] {response}")
    
    def run(self):
        """运行演示"""
        if not self.setup_agent():
            return
            
        while True:
            try:
                self.show_menu()
                choice = Prompt.ask("\n请选择功能", choices=["0", "1", "2", "3", "4", "5", "6"])
                
                if choice == "0":
                    self.console.print("[yellow]感谢使用! 👋[/yellow]")
                    break
                elif choice == "1":
                    self.chat_mode()
                elif choice == "2":
                    self.math_demo()
                elif choice == "3":
                    self.knowledge_demo()
                elif choice == "4":
                    self.code_demo()
                elif choice == "5":
                    self.memory_demo()
                elif choice == "6":
                    self.tech_demo()
                    
            except KeyboardInterrupt:
                self.console.print("\n[yellow]程序已退出[/yellow]")
                break
            except Exception as e:
                self.console.print(f"[red]错误: {e}[/red]")


if __name__ == "__main__":
    demo = AgentDemo()
    demo.run()
