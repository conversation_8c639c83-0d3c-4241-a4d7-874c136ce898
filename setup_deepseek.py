#!/usr/bin/env python3
"""
DeepSeek 快速设置脚本

自动配置DeepSeek API并测试Contextual AI Agent
"""

import os
import subprocess
import sys
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm

def install_dependencies():
    """安装必要的依赖"""
    console = Console()
    
    console.print("[yellow]检查并安装依赖...[/yellow]")
    
    try:
        # 检查是否已安装openai
        import openai
        console.print("[green]✓ OpenAI SDK 已安装[/green]")
    except ImportError:
        console.print("[yellow]安装 OpenAI SDK...[/yellow]")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "openai>=1.0.0"])
        console.print("[green]✓ OpenAI SDK 安装完成[/green]")
    
    # 安装其他依赖
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        console.print("[green]✓ 所有依赖安装完成[/green]")
    except subprocess.CalledProcessError as e:
        console.print(f"[red]依赖安装失败: {e}[/red]")
        return False
    
    return True

def setup_env_file():
    """设置环境变量文件"""
    console = Console()
    
    # 检查是否已存在.env文件
    if os.path.exists(".env"):
        if not Confirm.ask("发现已存在.env文件，是否覆盖？"):
            return True
    
    # 获取DeepSeek API密钥
    console.print("\n[bold cyan]设置DeepSeek API密钥[/bold cyan]")
    console.print("您可以从 https://platform.deepseek.com/ 获取API密钥")
    
    # 使用您提供的密钥作为默认值
    default_key = "sk-2bbab1c0f92d4309ae45716761a45f59"
    deepseek_key = Prompt.ask(
        "请输入DeepSeek API密钥", 
        default=default_key,
        show_default=False
    )
    
    # 可选的OpenAI密钥
    openai_key = Prompt.ask(
        "请输入OpenAI API密钥 (用于嵌入，可选)", 
        default="",
        show_default=False
    )
    
    # 创建.env文件
    env_content = f"""# DeepSeek API Configuration
DEEPSEEK_API_KEY={deepseek_key}
MODEL_PREFERENCE=deepseek

# OpenAI API Key (for embeddings)
OPENAI_API_KEY={openai_key}

# Optional: LangSmith for tracing
LANGCHAIN_TRACING_V2=false
LANGCHAIN_API_KEY=
LANGCHAIN_PROJECT=contextual-engineering-guide
"""
    
    with open(".env", "w", encoding="utf-8") as f:
        f.write(env_content)
    
    console.print("[green]✓ .env 文件创建成功[/green]")
    return True

def test_deepseek_connection():
    """测试DeepSeek连接"""
    console = Console()
    
    console.print("\n[yellow]测试DeepSeek API连接...[/yellow]")
    
    try:
        from openai import OpenAI
        
        # 从环境变量读取API密钥
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv("DEEPSEEK_API_KEY")
        if not api_key:
            console.print("[red]❌ 未找到DeepSeek API密钥[/red]")
            return False
        
        client = OpenAI(
            api_key=api_key,
            base_url="https://api.deepseek.com"
        )
        
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Hello, please respond with 'DeepSeek API is working!'"}
            ],
            stream=False
        )
        
        result = response.choices[0].message.content
        console.print(f"[green]✓ DeepSeek API 连接成功![/green]")
        console.print(f"[cyan]响应: {result}[/cyan]")
        return True
        
    except Exception as e:
        console.print(f"[red]❌ DeepSeek API 连接失败: {e}[/red]")
        return False

def test_agent():
    """测试Contextual AI Agent"""
    console = Console()
    
    console.print("\n[yellow]测试Contextual AI Agent...[/yellow]")
    
    try:
        from contextual_ai_agent import ContextualAIAgent
        
        agent = ContextualAIAgent()
        
        # 简单测试
        response = agent.chat("你好，请简单介绍一下你自己")
        console.print(f"[green]✓ Agent 测试成功![/green]")
        console.print(f"[cyan]Agent回复: {response[:100]}...[/cyan]")
        
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Agent 测试失败: {e}[/red]")
        return False

def main():
    """主函数"""
    console = Console()
    
    console.print(Panel.fit(
        "[bold blue]🚀 DeepSeek + Contextual AI Agent[/bold blue]\n"
        "[yellow]快速设置和配置向导[/yellow]\n\n"
        "[green]这个脚本将帮助您:[/green]\n"
        "• 安装必要的依赖\n"
        "• 配置DeepSeek API\n"
        "• 测试Agent功能",
        title="Setup Wizard"
    ))
    
    # 步骤1: 安装依赖
    console.print("\n[bold cyan]步骤 1: 安装依赖[/bold cyan]")
    if not install_dependencies():
        console.print("[red]❌ 依赖安装失败，请手动安装[/red]")
        return 1
    
    # 步骤2: 设置环境文件
    console.print("\n[bold cyan]步骤 2: 配置API密钥[/bold cyan]")
    if not setup_env_file():
        console.print("[red]❌ 环境配置失败[/red]")
        return 1
    
    # 步骤3: 测试连接
    console.print("\n[bold cyan]步骤 3: 测试API连接[/bold cyan]")
    if not test_deepseek_connection():
        console.print("[red]❌ API连接测试失败[/red]")
        return 1
    
    # 步骤4: 测试Agent
    console.print("\n[bold cyan]步骤 4: 测试AI Agent[/bold cyan]")
    if not test_agent():
        console.print("[red]❌ Agent测试失败[/red]")
        return 1
    
    # 完成
    console.print(Panel.fit(
        "[bold green]🎉 设置完成![/bold green]\n\n"
        "[yellow]现在您可以使用以下命令:[/yellow]\n"
        "• python deepseek_example.py  # DeepSeek专用示例\n"
        "• python run.py --mode demo   # 完整演示\n"
        "• python run.py --mode chat   # 直接聊天\n"
        "• python run.py --mode test   # 功能测试",
        title="🚀 Ready to Go!"
    ))
    
    # 询问是否立即运行演示
    if Confirm.ask("\n是否立即运行DeepSeek演示？"):
        try:
            import deepseek_example
            deepseek_example.main()
        except Exception as e:
            console.print(f"[red]演示运行失败: {e}[/red]")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
