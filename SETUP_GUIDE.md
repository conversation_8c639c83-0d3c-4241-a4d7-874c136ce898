# Contextual AI Agent 安装和使用指南

## 🎯 项目概述

这是一个集成了所有上下文工程技术的综合AI Agent，展示了LangChain和LangGraph的强大功能。

### 🔧 核心技术

1. **Write (写入上下文)**
   - StateGraph: 状态管理和工作流编排
   - InMemoryStore: 长期记忆存储
   - Checkpointing: 会话状态持久化

2. **Select (选择上下文)**
   - 状态选择: 从当前会话状态中选择相关信息
   - 记忆选择: 从长期记忆中检索相关内容
   - RAG检索: 文档知识库检索
   - BigTool: 语义相似度工具选择

3. **Compress (压缩上下文)**
   - 对话总结: 长对话历史压缩
   - RAG结果压缩: 检索内容的智能压缩

4. **Isolate (隔离上下文)**
   - 子代理架构: 专门化代理分工
   - Supervisor模式: 代理协调管理
   - 沙盒环境: 安全代码执行

## 🚀 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv contextual_agent_env

# 激活虚拟环境
# Windows:
contextual_agent_env\Scripts\activate
# macOS/Linux:
source contextual_agent_env/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. API密钥配置

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填入您的API密钥：
```env
# 必需的API密钥
ANTHROPIC_API_KEY=your_anthropic_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# 可选的LangSmith追踪
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your_langsmith_api_key_here
LANGCHAIN_PROJECT=contextual-engineering-guide
```

### 3. 运行演示

```bash
# 运行交互式演示
python demo_agent.py

# 或者直接使用Agent
python contextual_ai_agent.py
```

## 📚 功能演示

### 1. 智能聊天模式
- 自动任务分析和分类
- 智能代理路由
- 上下文压缩和记忆管理

### 2. 数学计算 (BigTool技术)
```python
# 示例查询
"计算 sin(π/4) 的值"
"求 log(100) 的值"
"计算 sqrt(16) 的结果"
```

### 3. 知识查询 (RAG技术)
```python
# 示例查询
"什么是人工智能？"
"机器学习和深度学习的区别是什么？"
```

### 4. 代码执行 (沙盒技术)
```python
# 示例任务
"写一个计算斐波那契数列的Python函数"
"创建一个简单的排序算法"
```

### 5. 记忆管理
- 长期记忆存储和检索
- 会话状态管理
- 上下文压缩

## 🔧 自定义配置

### 添加自定义知识库

```python
from contextual_ai_agent import ContextualAIAgent

agent = ContextualAIAgent()

# 添加自定义知识
agent.add_knowledge(
    content="您的知识内容",
    title="知识标题"
)
```

### 扩展工具集

```python
# 在 _setup_tools 方法中添加自定义工具
def custom_tool(input_text: str) -> str:
    """自定义工具描述"""
    return f"处理结果: {input_text}"

# 将工具添加到工具注册表
self.tool_registry["custom_tool"] = custom_tool
```

### 配置子代理

```python
# 在 _setup_sub_agents 方法中自定义代理
custom_agent = create_react_agent(
    model=self.llm,
    tools=[your_tools],
    name="custom_expert",
    prompt="您的自定义提示"
)
```

## 🐛 故障排除

### 常见问题

1. **API密钥错误**
   ```
   错误: Missing API key
   解决: 检查 .env 文件中的API密钥配置
   ```

2. **依赖安装失败**
   ```bash
   # 升级pip
   pip install --upgrade pip
   
   # 重新安装依赖
   pip install -r requirements.txt --force-reinstall
   ```

3. **沙盒环境问题**
   ```
   错误: Deno not found
   解决: 安装Deno - https://docs.deno.com/runtime/getting_started/installation/
   ```

4. **内存不足**
   ```python
   # 在配置中限制历史长度
   max_history_length = 10
   ```

### 性能优化

1. **减少工具数量**: 限制BigTool中的工具数量
2. **压缩频率**: 调整历史压缩的触发条件
3. **缓存设置**: 启用向量存储缓存

## 📖 技术文档

### 架构图

```
用户输入 → 任务分析 → 上下文选择 → 历史压缩 → 代理路由 → 任务执行 → 记忆更新
    ↓         ↓         ↓         ↓         ↓         ↓         ↓
  Write    Select   Compress   Isolate   Execute   Update   Response
```

### 状态管理

```python
class ContextualAgentState(TypedDict):
    messages: List[Any]           # 对话历史
    current_task: str            # 当前任务
    task_type: str              # 任务类型
    working_memory: Dict        # 工作记忆
    compressed_history: str     # 压缩历史
    retrieved_knowledge: List   # 检索知识
    tool_usage_history: List    # 工具使用历史
    assigned_agent: str         # 分配的代理
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [LangChain](https://github.com/langchain-ai/langchain) - 核心框架
- [LangGraph](https://github.com/langchain-ai/langgraph) - 工作流编排
- [Anthropic](https://www.anthropic.com/) - Claude模型
- [OpenAI](https://openai.com/) - 嵌入模型

---

**Happy Coding! 🚀**
