#!/usr/bin/env python3
"""
Contextual AI Agent 测试脚本

这个脚本用于测试Agent的各项功能，确保所有上下文工程技术正常工作。
"""

import os
import sys
import traceback
from contextual_ai_agent import ContextualAIAgent
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

class AgentTester:
    def __init__(self):
        self.console = Console()
        self.agent = None
        self.test_results = {}
        
    def setup_test_environment(self):
        """设置测试环境"""
        self.console.print(Panel.fit(
            "[bold blue]Contextual AI Agent 测试套件[/bold blue]\n"
            "验证所有上下文工程技术的功能",
            title="🧪 Agent Testing"
        ))
        
        # 检查环境变量
        required_keys = ["ANTHROPIC_API_KEY", "OPENAI_API_KEY"]
        missing_keys = [key for key in required_keys if not os.getenv(key)]
        
        if missing_keys:
            self.console.print(f"[red]❌ 缺少必需的环境变量: {', '.join(missing_keys)}[/red]")
            self.console.print("[yellow]请设置API密钥后重试[/yellow]")
            return False
            
        return True
    
    def test_agent_initialization(self):
        """测试Agent初始化"""
        self.console.print("\n[bold cyan]🔧 测试Agent初始化...[/bold cyan]")
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task("初始化Agent...", total=None)
                self.agent = ContextualAIAgent()
                progress.update(task, description="✓ Agent初始化完成")
                
            self.test_results["initialization"] = {"status": "✓", "message": "成功"}
            self.console.print("[green]✓ Agent初始化测试通过[/green]")
            return True
            
        except Exception as e:
            self.test_results["initialization"] = {"status": "❌", "message": str(e)}
            self.console.print(f"[red]❌ Agent初始化失败: {e}[/red]")
            return False
    
    def test_write_technologies(self):
        """测试Write技术 - 状态管理和记忆写入"""
        self.console.print("\n[bold cyan]📝 测试Write技术...[/bold cyan]")
        
        tests = [
            ("状态管理", "测试基本对话", "你好，我是测试用户"),
            ("记忆写入", "测试记忆存储", "请记住我的名字是测试员"),
            ("会话持久化", "测试会话状态", "我刚才说了什么？")
        ]
        
        write_results = []
        
        for test_name, description, query in tests:
            try:
                self.console.print(f"  • {test_name}: {description}")
                response = self.agent.chat(query, f"write_test_{test_name}")
                
                if response and len(response) > 0:
                    write_results.append({"test": test_name, "status": "✓", "response": response[:50] + "..."})
                    self.console.print(f"    [green]✓ {test_name} 通过[/green]")
                else:
                    write_results.append({"test": test_name, "status": "❌", "response": "无响应"})
                    self.console.print(f"    [red]❌ {test_name} 失败[/red]")
                    
            except Exception as e:
                write_results.append({"test": test_name, "status": "❌", "response": str(e)})
                self.console.print(f"    [red]❌ {test_name} 错误: {e}[/red]")
        
        self.test_results["write"] = write_results
        success_count = sum(1 for r in write_results if r["status"] == "✓")
        self.console.print(f"[blue]Write技术测试完成: {success_count}/{len(tests)} 通过[/blue]")
    
    def test_select_technologies(self):
        """测试Select技术 - 上下文选择"""
        self.console.print("\n[bold cyan]🎯 测试Select技术...[/bold cyan]")
        
        tests = [
            ("状态选择", "测试状态信息选择", "根据我们之前的对话，总结一下"),
            ("记忆选择", "测试记忆检索", "你还记得我的名字吗？"),
            ("知识检索", "测试RAG功能", "什么是人工智能？"),
            ("工具选择", "测试BigTool", "计算sin(0.5)的值")
        ]
        
        select_results = []
        
        for test_name, description, query in tests:
            try:
                self.console.print(f"  • {test_name}: {description}")
                response = self.agent.chat(query, f"select_test_{test_name}")
                
                if response and len(response) > 0:
                    select_results.append({"test": test_name, "status": "✓", "response": response[:50] + "..."})
                    self.console.print(f"    [green]✓ {test_name} 通过[/green]")
                else:
                    select_results.append({"test": test_name, "status": "❌", "response": "无响应"})
                    self.console.print(f"    [red]❌ {test_name} 失败[/red]")
                    
            except Exception as e:
                select_results.append({"test": test_name, "status": "❌", "response": str(e)})
                self.console.print(f"    [red]❌ {test_name} 错误: {e}[/red]")
        
        self.test_results["select"] = select_results
        success_count = sum(1 for r in select_results if r["status"] == "✓")
        self.console.print(f"[blue]Select技术测试完成: {success_count}/{len(tests)} 通过[/blue]")
    
    def test_compress_technologies(self):
        """测试Compress技术 - 上下文压缩"""
        self.console.print("\n[bold cyan]🗜️ 测试Compress技术...[/bold cyan]")
        
        # 创建长对话历史
        long_conversation = [
            "第一条消息：介绍项目背景",
            "第二条消息：讨论技术细节", 
            "第三条消息：分析实现方案",
            "第四条消息：评估性能指标",
            "第五条消息：制定测试计划",
            "第六条消息：安排项目进度",
            "第七条消息：分配团队任务",
            "第八条消息：确定交付时间",
            "第九条消息：讨论风险控制",
            "第十条消息：总结会议要点",
            "现在请总结我们之前讨论的所有内容"
        ]
        
        compress_results = []
        thread_id = "compress_test"
        
        try:
            # 发送多条消息创建长历史
            for i, message in enumerate(long_conversation[:-1]):
                self.agent.chat(message, thread_id)
                
            # 测试压缩功能
            self.console.print("  • 对话历史压缩: 测试长对话压缩")
            final_response = self.agent.chat(long_conversation[-1], thread_id)
            
            if final_response and "总结" in final_response:
                compress_results.append({"test": "对话压缩", "status": "✓", "response": final_response[:50] + "..."})
                self.console.print("    [green]✓ 对话历史压缩 通过[/green]")
            else:
                compress_results.append({"test": "对话压缩", "status": "❌", "response": "压缩失败"})
                self.console.print("    [red]❌ 对话历史压缩 失败[/red]")
                
        except Exception as e:
            compress_results.append({"test": "对话压缩", "status": "❌", "response": str(e)})
            self.console.print(f"    [red]❌ 对话历史压缩 错误: {e}[/red]")
        
        self.test_results["compress"] = compress_results
        success_count = sum(1 for r in compress_results if r["status"] == "✓")
        self.console.print(f"[blue]Compress技术测试完成: {success_count}/1 通过[/blue]")
    
    def test_isolate_technologies(self):
        """测试Isolate技术 - 上下文隔离"""
        self.console.print("\n[bold cyan]🔒 测试Isolate技术...[/bold cyan]")
        
        tests = [
            ("数学代理", "测试数学专家代理", "计算平方根16"),
            ("研究代理", "测试研究专家代理", "查询机器学习相关信息"),
            ("代码代理", "测试代码专家代理", "写一个Hello World程序"),
            ("代理协调", "测试监督者协调", "我需要研究AI并计算一些数据")
        ]
        
        isolate_results = []
        
        for test_name, description, query in tests:
            try:
                self.console.print(f"  • {test_name}: {description}")
                response = self.agent.chat(query, f"isolate_test_{test_name}")
                
                if response and len(response) > 0:
                    isolate_results.append({"test": test_name, "status": "✓", "response": response[:50] + "..."})
                    self.console.print(f"    [green]✓ {test_name} 通过[/green]")
                else:
                    isolate_results.append({"test": test_name, "status": "❌", "response": "无响应"})
                    self.console.print(f"    [red]❌ {test_name} 失败[/red]")
                    
            except Exception as e:
                isolate_results.append({"test": test_name, "status": "❌", "response": str(e)})
                self.console.print(f"    [red]❌ {test_name} 错误: {e}[/red]")
        
        self.test_results["isolate"] = isolate_results
        success_count = sum(1 for r in isolate_results if r["status"] == "✓")
        self.console.print(f"[blue]Isolate技术测试完成: {success_count}/{len(tests)} 通过[/blue]")
    
    def generate_test_report(self):
        """生成测试报告"""
        self.console.print("\n[bold magenta]📊 测试报告[/bold magenta]")
        
        # 创建总结表格
        table = Table(title="🧪 Contextual AI Agent 测试结果")
        table.add_column("技术类别", style="cyan", no_wrap=True)
        table.add_column("测试项目", style="white")
        table.add_column("状态", style="bold")
        table.add_column("备注", style="dim")
        
        # 添加初始化结果
        init_result = self.test_results.get("initialization", {})
        table.add_row(
            "初始化", 
            "Agent创建", 
            init_result.get("status", "❌"),
            init_result.get("message", "未测试")[:30]
        )
        
        # 添加各技术测试结果
        for tech_name, tech_results in self.test_results.items():
            if tech_name == "initialization":
                continue
                
            if isinstance(tech_results, list):
                for result in tech_results:
                    table.add_row(
                        tech_name.upper(),
                        result["test"],
                        result["status"],
                        result["response"][:30] if result["response"] else ""
                    )
        
        self.console.print(table)
        
        # 计算总体成功率
        total_tests = 0
        successful_tests = 0
        
        for tech_results in self.test_results.values():
            if isinstance(tech_results, list):
                total_tests += len(tech_results)
                successful_tests += sum(1 for r in tech_results if r["status"] == "✓")
            elif isinstance(tech_results, dict) and tech_results.get("status") == "✓":
                total_tests += 1
                successful_tests += 1
        
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        
        self.console.print(f"\n[bold blue]总体测试结果: {successful_tests}/{total_tests} 通过 ({success_rate:.1f}%)[/bold blue]")
        
        if success_rate >= 80:
            self.console.print("[bold green]🎉 Agent功能正常，可以投入使用！[/bold green]")
        elif success_rate >= 60:
            self.console.print("[bold yellow]⚠️ Agent基本功能正常，部分功能需要优化[/bold yellow]")
        else:
            self.console.print("[bold red]❌ Agent存在严重问题，需要修复后再使用[/bold red]")
    
    def run_all_tests(self):
        """运行所有测试"""
        if not self.setup_test_environment():
            return False
            
        if not self.test_agent_initialization():
            return False
        
        # 运行各项技术测试
        self.test_write_technologies()
        self.test_select_technologies()
        self.test_compress_technologies()
        self.test_isolate_technologies()
        
        # 生成测试报告
        self.generate_test_report()
        
        return True


def main():
    """主函数"""
    tester = AgentTester()
    
    try:
        success = tester.run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        tester.console.print("\n[yellow]测试被用户中断[/yellow]")
        return 1
    except Exception as e:
        tester.console.print(f"\n[red]测试过程中发生错误: {e}[/red]")
        tester.console.print(f"[dim]{traceback.format_exc()}[/dim]")
        return 1


if __name__ == "__main__":
    sys.exit(main())
