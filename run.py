#!/usr/bin/env python3
"""
Contextual AI Agent 启动脚本

快速启动和使用Contextual AI Agent的入口脚本。
"""

import os
import sys
import argparse
from rich.console import Console
from rich.panel import Panel

def main():
    console = Console()
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="Contextual AI Agent 启动器")
    parser.add_argument("--mode", choices=["demo", "test", "chat"], default="demo",
                       help="运行模式: demo(演示), test(测试), chat(直接聊天)")
    parser.add_argument("--thread-id", default="main", help="会话线程ID")
    
    args = parser.parse_args()
    
    # 显示欢迎信息
    console.print(Panel.fit(
        "[bold blue]🤖 Contextual AI Agent[/bold blue]\n"
        "[yellow]集成上下文工程技术的智能助手[/yellow]\n\n"
        f"[green]运行模式: {args.mode}[/green]",
        title="Welcome"
    ))
    
    # 检查环境
    if not os.getenv("ANTHROPIC_API_KEY") or not os.getenv("OPENAI_API_KEY"):
        console.print("[red]❌ 请先设置API密钥![/red]")
        console.print("1. 复制 .env.example 到 .env")
        console.print("2. 在 .env 文件中填入您的API密钥")
        return 1
    
    try:
        if args.mode == "demo":
            # 运行演示模式
            from demo_agent import AgentDemo
            demo = AgentDemo()
            demo.run()
            
        elif args.mode == "test":
            # 运行测试模式
            from test_agent import AgentTester
            tester = AgentTester()
            success = tester.run_all_tests()
            return 0 if success else 1
            
        elif args.mode == "chat":
            # 直接聊天模式
            from contextual_ai_agent import ContextualAIAgent
            
            console.print("[yellow]初始化Agent...[/yellow]")
            agent = ContextualAIAgent()
            console.print("[green]✓ 初始化完成![/green]\n")
            
            console.print("[dim]输入 'quit' 退出[/dim]")
            
            while True:
                try:
                    user_input = input("\n您: ")
                    if user_input.lower() in ['quit', 'exit', '退出']:
                        break
                        
                    response = agent.chat(user_input, args.thread_id)
                    console.print(f"[green]AI:[/green] {response}")
                    
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    console.print(f"[red]错误: {e}[/red]")
        
        return 0
        
    except ImportError as e:
        console.print(f"[red]导入错误: {e}[/red]")
        console.print("请确保已安装所有依赖: pip install -r requirements.txt")
        return 1
    except Exception as e:
        console.print(f"[red]运行错误: {e}[/red]")
        return 1

if __name__ == "__main__":
    sys.exit(main())
