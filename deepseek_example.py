#!/usr/bin/env python3
"""
DeepSeek API 集成示例

展示如何使用DeepSeek API与Contextual AI Agent
"""

import os
from contextual_ai_agent import ContextualAIAgent
from rich.console import Console
from rich.panel import Panel

def setup_deepseek():
    """设置DeepSeek API"""
    console = Console()
    
    # 设置DeepSeek API密钥
    deepseek_api_key = "***********************************"  # 您提供的密钥
    
    # 设置环境变量
    os.environ["DEEPSEEK_API_KEY"] = deepseek_api_key
    os.environ["MODEL_PREFERENCE"] = "deepseek"
    
    # 设置OpenAI API密钥用于嵌入（如果有的话）
    if not os.getenv("OPENAI_API_KEY"):
        console.print("[yellow]注意: 未设置OpenAI API密钥，某些功能可能受限[/yellow]")
        # 可以使用免费的嵌入服务或跳过嵌入功能
        os.environ["OPENAI_API_KEY"] = "dummy_key"  # 临时设置
    
    console.print(Panel.fit(
        "[bold green]✓ DeepSeek API 配置完成[/bold green]\n"
        f"[cyan]API密钥: {deepseek_api_key[:20]}...[/cyan]\n"
        "[yellow]模型: deepseek-chat[/yellow]",
        title="🚀 DeepSeek Setup"
    ))

def test_deepseek_basic():
    """测试DeepSeek基础功能"""
    console = Console()
    console.print("\n[bold cyan]🧪 测试DeepSeek基础功能[/bold cyan]")
    
    try:
        # 创建Agent实例
        agent = ContextualAIAgent()
        
        # 测试基础对话
        test_queries = [
            "你好，请介绍一下你自己",
            "什么是人工智能？",
            "计算 2 + 3 的结果",
            "写一个简单的Python函数来计算阶乘"
        ]
        
        for i, query in enumerate(test_queries, 1):
            console.print(f"\n[blue]测试 {i}: {query}[/blue]")
            response = agent.chat(query, f"deepseek_test_{i}")
            console.print(f"[green]回复:[/green] {response}")
            
    except Exception as e:
        console.print(f"[red]测试失败: {e}[/red]")

def test_deepseek_advanced():
    """测试DeepSeek高级功能"""
    console = Console()
    console.print("\n[bold cyan]🔬 测试DeepSeek高级功能[/bold cyan]")
    
    try:
        agent = ContextualAIAgent()
        
        # 测试记忆功能
        console.print("\n[yellow]测试记忆功能...[/yellow]")
        agent.chat("请记住我的名字是张三，我喜欢编程", "memory_test")
        response = agent.chat("你还记得我的名字和爱好吗？", "memory_test")
        console.print(f"[green]记忆测试:[/green] {response}")
        
        # 测试任务分类
        console.print("\n[yellow]测试任务分类...[/yellow]")
        complex_query = "我需要研究机器学习的基础知识，然后计算一些统计数据"
        response = agent.chat(complex_query, "classification_test")
        console.print(f"[green]复杂任务处理:[/green] {response}")
        
        # 测试上下文压缩
        console.print("\n[yellow]测试上下文压缩...[/yellow]")
        for i in range(5):
            agent.chat(f"这是第{i+1}条消息，讨论关于AI的话题", "compression_test")
        
        final_response = agent.chat("请总结我们之前讨论的内容", "compression_test")
        console.print(f"[green]压缩总结:[/green] {final_response}")
        
    except Exception as e:
        console.print(f"[red]高级测试失败: {e}[/red]")

def interactive_chat():
    """交互式聊天"""
    console = Console()
    console.print("\n[bold cyan]💬 DeepSeek 交互式聊天[/bold cyan]")
    console.print("[dim]输入 'quit' 退出[/dim]\n")
    
    try:
        agent = ContextualAIAgent()
        
        while True:
            user_input = input("\n您: ")
            if user_input.lower() in ['quit', 'exit', '退出']:
                break
                
            response = agent.chat(user_input, "interactive")
            console.print(f"[green]DeepSeek:[/green] {response}")
            
    except KeyboardInterrupt:
        console.print("\n[yellow]聊天结束[/yellow]")
    except Exception as e:
        console.print(f"[red]聊天错误: {e}[/red]")

def main():
    """主函数"""
    console = Console()
    
    console.print(Panel.fit(
        "[bold blue]🤖 DeepSeek + Contextual AI Agent[/bold blue]\n"
        "[yellow]集成DeepSeek API的智能助手演示[/yellow]",
        title="Welcome"
    ))
    
    # 设置DeepSeek
    setup_deepseek()
    
    while True:
        console.print("\n[bold cyan]选择测试模式:[/bold cyan]")
        console.print("1. 基础功能测试")
        console.print("2. 高级功能测试") 
        console.print("3. 交互式聊天")
        console.print("0. 退出")
        
        choice = input("\n请选择 (0-3): ")
        
        if choice == "0":
            console.print("[yellow]再见! 👋[/yellow]")
            break
        elif choice == "1":
            test_deepseek_basic()
        elif choice == "2":
            test_deepseek_advanced()
        elif choice == "3":
            interactive_chat()
        else:
            console.print("[red]无效选择，请重试[/red]")

if __name__ == "__main__":
    main()
